# 交易工具
class TradeUtils:
    cash: float = 0.0
    strategy = None
    total_pos = 0.0

    def __init__(self, strategy):
        self.cash = strategy.cta_engine.capital
        self.strategy = strategy

    # 根据资金，按比例计算交易量
    def calc_target_share(self, close_price, rate, use_cash=True):
        if rate < 0:
            rate = 0
        if rate > 1:
            rate = 1
        return (self.cash if use_cash else self.strategy.cta_engine.capital) / (close_price*(1+self.strategy.cta_engine.rate)) * rate

    # 根据当前资金，按比例买入
    def buy_share(self, close_price, rate):
        if rate < 0:
            rate = 0
        if rate > 1:
            rate = 1
        self.strategy.buy(close_price, self.calc_target_share(close_price, rate))
    
    # 根据持有，按比例卖出
    def sell_share(self, close_price, rate, use_total_pos=False):
        if rate < 0:
            rate = 0
        if rate >= 1:
            rate = 1
            # 全部卖出
            self.strategy.sell(close_price, self.strategy.pos)
            return
        # 按总金额计算全仓位
        pos = self.calc_target_share(close_price, rate, False)
        # use_total_pos 按持有计算仓位
        pos = self.total_pos * rate if use_total_pos else pos
        if pos > self.strategy.pos:
            pos = self.strategy.pos
        self.strategy.sell(close_price, pos)